package com.hntnbs.ai.survey.service.agent.loader;

import com.hntnbs.ai.survey.config.records.ThinkActRecord;
import com.hntnbs.ai.survey.context.input.UserInputService;
import com.hntnbs.ai.survey.context.properties.TnbsAIProperties;
import com.hntnbs.ai.survey.service.agent.base.LlmService;
import com.hntnbs.ai.survey.service.agent.fun_tools.ToolCallbackProvider;
import com.hntnbs.ai.survey.service.agent.reAct.ReActAgent;
import com.hntnbs.ai.survey.service.agent.recorders.PlanExecutionRecorder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;
import java.util.Map;

@Slf4j
@Setter
@Getter
public class DynamicAgent extends ReActAgent {
    private static final String CURRENT_STEP_ENV_DATA_KEY = "current_step_env_data";

    private String agentName;

    private String agentDescription;

    private String nextStepPrompt;

    private ToolCallbackProvider toolCallbackProvider;

    private List<String> availableToolKeys;

    private ChatResponse response;

    private Prompt userPrompt;

    protected ThinkActRecord thinkActRecord;

    private ToolCallingManager toolCallingManager;

    private UserInputService userInputService;

    /**
     * 构造函数
     *
     * @param llmService            LLM服务实例，用于处理自然语言交互
     * @param planExecutionRecorder 计划执行记录器，用于记录执行过程
     * @param tnbsAIProperties       Manus配置属性
     */
    public DynamicAgent(LlmService llmService, PlanExecutionRecorder planExecutionRecorder,
                        TnbsAIProperties tnbsAIProperties, String name, String description, String nextStepPrompt,
                        List<String> availableToolKeys, ToolCallingManager toolCallingManager,
                        Map<String, Object> initialAgentSetting, UserInputService userInputService) {
        super(llmService, planExecutionRecorder, tnbsAIProperties, initialAgentSetting);
        this.agentName = name;
        this.agentDescription = description;
        this.nextStepPrompt = nextStepPrompt;
        this.availableToolKeys = availableToolKeys;
        this.toolCallingManager = toolCallingManager;
        this.userInputService = userInputService;
    }


    @Override
    protected boolean think() {
        return false;
    }

    @Override
    protected AgentExecResult act() {
        return null;
    }

    @Override
    public void clearUp(String planId) {

    }

    @Override
    public String getName() {
        return "";
    }

    @Override
    public String getDescription() {
        return "";
    }

    @Override
    protected Message getNextStepWithEnvMessage() {
        return null;
    }

    @Override
    public List<ToolCallback> getToolCallList() {
        return List.of();
    }
}