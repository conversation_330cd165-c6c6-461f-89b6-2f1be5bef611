package com.hntnbs.ai.survey.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntnbs.ai.survey.controller.exception.ex.AgentActionException;
import com.hntnbs.ai.survey.mapper.DynamicAgentMapper;
import com.hntnbs.ai.survey.model.DynamicAgentEntity;
import com.hntnbs.ai.survey.service.AgentService;
import com.hntnbs.ai.survey.constant.ConstantDB;
import com.hntnbs.ai.survey.service.agent.base.BaseAgent;
import com.hntnbs.ai.survey.service.agent.fun_tools.ToolCallbackProvider;
import com.hntnbs.ai.survey.service.agent.loader.DynamicAgent;
import com.hntnbs.ai.survey.service.agent.loader.DynamicAgentLoader;
import com.hntnbs.ai.survey.service.agent.planning.PlanningFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 对话消息表(TEAiMessage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-06 18:33:56
 */
@Service("agentServiceImpl")
@Slf4j
public class AgentServiceImpl extends ServiceImpl<DynamicAgentMapper, DynamicAgentEntity> implements AgentService {

    private final DynamicAgentLoader dynamicAgentLoader;

    private final PlanningFactory planningFactory;

    @Autowired
    public AgentServiceImpl(@Lazy DynamicAgentLoader dynamicAgentLoader,
                            @Lazy PlanningFactory planningFactory) {
        this.dynamicAgentLoader = dynamicAgentLoader;
        this.planningFactory = planningFactory;
    }

    @Override
    public DynamicAgentEntity findByAgentName(String agentName) {
        List<DynamicAgentEntity> records = lambdaQuery().eq(DynamicAgentEntity::getAgentName, agentName).list();
        if (records.size() > 1){
            throw new AgentActionException("存在多个同名的Agent: " + agentName);
        }
        return lambdaQuery().eq(DynamicAgentEntity::getAgentName, agentName).one();
    }

    @Override
    public List<DynamicAgentEntity> findAll() {
        return lambdaQuery().eq(DynamicAgentEntity::getDeleteFlag, ConstantDB.DELETE_FLAG).list();
    }

    @Override
    public BaseAgent createDynamicBaseAgent(String name, String planId, Map<String, Object> initialAgentSetting) {

        log.info("连接到Agent进行分析: {}, planId: {}", name, planId);

        try {
            // 通过dynamicAgentLoader加载已存在的Agent
            DynamicAgent agent = dynamicAgentLoader.loadAgent(name, initialAgentSetting);

            // 设置planId
            agent.setPlanId(planId);
            // 设置工具回调映射
            Map<String, PlanningFactory.ToolCallBackContext> toolCallbackMap = planningFactory.toolCallbackMap(planId);
            agent.setToolCallbackProvider(() -> toolCallbackMap);

            log.info("成功加载Agent: {}, 可用工具数量: {}", name, agent.getToolCallList().size());

            return agent;
        }
        catch (Exception e) {
            log.error("加载BaseAgent过程中发生异常: {}, 错误信息: {}", name, e.getMessage(), e);
            throw new RuntimeException("加载BaseAgent失败: " + e.getMessage(), e);
        }
    }
}

