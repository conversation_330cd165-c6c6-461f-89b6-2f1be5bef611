
package com.hntnbs.ai.survey.service.agent.base;

import com.hntnbs.ai.survey.config.records.AgentExecutionRecord;
import com.hntnbs.ai.survey.context.properties.TnbsAIProperties;
import com.hntnbs.ai.survey.enums.AgentState;
import com.hntnbs.ai.survey.service.agent.recorders.PlanExecutionRecorder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.tool.ToolCallback;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * An abstract base class for implementing AI agents that can execute multi-step tasks.
 * This class provides the core functionality for managing agent state, conversation flow,
 * and step-by-step execution of tasks.
 *
 * <p>
 * The agent supports a finite number of execution steps and includes mechanisms for:
 * <ul>
 * <li>State management (idle, running, finished)</li>
 * <li>Conversation tracking</li>
 * <li>Step limitation and monitoring</li>
 * <li>Thread-safe execution</li>
 * <li>Stuck-state detection and handling</li>
 * </ul>
 *
 * <p>
 * Implementing classes must define:
 * <ul>
 * <li>{@link #getName()} - Returns the agent's name</li>
 * <li>{@link #getDescription()} - Returns the agent's description</li>
 * <li>{@link #getThinkMessage()} - Implements the thinking chain logic</li>
 * <li>{@link #getNextStepWithEnvMessage()} - Provides the next step's prompt</li>
 * </ul>
 *
 */
@Slf4j
@Getter
@Setter
public abstract class BaseAgent {

    private String planId = null;

    private AgentState state = AgentState.NOT_STARTED;

	protected LlmService llmService;

    private final TnbsAIProperties tnbsAIProperties;

	private int maxSteps;

	private int currentStep = 0;

	// Change the data map to an immutable object and initialize it properly
	private final Map<String, Object> initSettingData;

    private Map<String, Object> envData = new HashMap<>();

	protected PlanExecutionRecorder planExecutionRecorder;

	public abstract void clearUp(String planId);

	/**
	 * 获取智能体的名称
	 *
	 * 实现要求： 1. 返回一个简短但具有描述性的名称 2. 名称应该反映该智能体的主要功能或特性 3. 名称应该是唯一的，便于日志和调试
	 *
	 * 示例实现： - ToolCallAgent 返回 "ToolCallAgent" - BrowserAgent 返回 "BrowserAgent"
	 * @return 智能体的名称
	 */
	public abstract String getName();

	/**
	 * 获取智能体的详细描述
	 *
	 * 实现要求： 1. 返回对该智能体功能的详细描述 2. 描述应包含智能体的主要职责和能力 3. 应说明该智能体与其他智能体的区别
	 *
	 * 示例实现： - ToolCallAgent: "负责管理和执行工具调用的智能体，支持多工具组合调用" - ReActAgent:
	 * "实现思考(Reasoning)和行动(Acting)交替执行的智能体"
	 * @return 智能体的详细描述文本
	 */
	public abstract String getDescription();

	/**
	 * 添加思考提示到消息列表中，构建智能体的思考链
	 *
	 * 实现要求： 1. 根据当前上下文和状态生成合适的系统提示词 2. 提示词应该指导智能体如何思考和决策 3. 可以递归地构建提示链，形成层次化的思考过程 4.
	 * 返回添加的系统提示消息对象
	 *
	 * 子类实现参考： 1. ReActAgent: 实现基础的思考-行动循环提示 2. ToolCallAgent: 添加工具选择和执行相关的提示
	 * @return 添加的系统提示消息对象
	 */
	protected Message getThinkMessage() {
		// 获取操作系统信息
		String osName = System.getProperty("os.name");
		String osVersion = System.getProperty("os.version");
		String osArch = System.getProperty("os.arch");

		// 获取当前日期时间，格式为yyyy-MM-dd
		String currentDateTime = java.time.LocalDate.now().toString(); // 格式为yyyy-MM-dd
		boolean isDebugModel = tnbsAIProperties.getAgentDebug();
		String detailOutput = "";
		if (isDebugModel) {
			detailOutput = """
					1. 使用工具调用时，必须给出解释说明，说明使用这个工具的理由和背后的思考
					2. 简述过去的所有步骤已经都做了什么事
					""";
		}
		else {
			detailOutput = """
					1. 使用工具调用时，不需要额外的任何解释说明！
					2. 不要在工具调用前提供推理或描述！
					""";

		}

		String stepPrompt = """
				- SYSTEM INFORMATION:
				OS: %s %s (%s)

				- Current Date:
				%s
				- 全局计划信息:
				{planStatus}

				- 当前要做的步骤要求(这个步骤是需要当前智能体完成的!) :
				STEP {currentStepIndex} :{stepText}

				- 当前步骤的上下文信息:
				{extraParams}

				重要说明：
				%s
				3. 做且只做当前要做的步骤要求中的内容
				4. 如果当前要做的步骤要求已经做完，则调用terminate工具来完成当前步骤。
				5. 全局目标 是用来有个全局认识的，不要在当前步骤中去完成这个全局目标。

				""".formatted(osName, osVersion, osArch, currentDateTime, detailOutput);

		SystemPromptTemplate promptTemplate = new SystemPromptTemplate(stepPrompt);

        return promptTemplate.createMessage(getInitSettingData());
	}

	/**
	 * 获取下一步操作的提示消息
	 *
	 * 实现要求： 1. 生成引导智能体执行下一步操作的提示消息 2. 提示内容应该基于当前执行状态和上下文 3. 消息应该清晰指导智能体要执行什么任务
	 *
	 * 子类实现参考： 1. ToolCallAgent：返回工具选择和调用相关的提示 2. ReActAgent：返回思考或行动决策相关的提示
	 * @return 下一步操作的提示消息对象
	 */
	protected abstract Message getNextStepWithEnvMessage();

	public abstract List<ToolCallback> getToolCallList();

	public BaseAgent(LlmService llmService, PlanExecutionRecorder planExecutionRecorder,
                     TnbsAIProperties tnbsAIProperties, Map<String, Object> initialAgentSetting) {
		this.llmService = llmService;
		this.planExecutionRecorder = planExecutionRecorder;
		this.tnbsAIProperties = tnbsAIProperties;
		this.maxSteps = tnbsAIProperties.getMaxSteps();
		this.initSettingData = Map.copyOf(initialAgentSetting);
	}

	public String run() {
		currentStep = 0;
		if (state != AgentState.IN_PROGRESS) {
			throw new IllegalStateException("Cannot run agent from state: " + state);
		}

		// Create agent execution record
		AgentExecutionRecord agentRecord = new AgentExecutionRecord(getPlanId(), getName(), getDescription());
		agentRecord.setMaxSteps(maxSteps);
		agentRecord.setStatus(state.toString());
		// Record execution in recorder if we have a plan ID
		if (planId != null && planExecutionRecorder != null) {
			planExecutionRecorder.recordAgentExecution(planId, agentRecord);
		}
		List<String> results = new ArrayList<>();
		try {
			state = AgentState.IN_PROGRESS;
			agentRecord.setStatus(state.toString());

			while (currentStep < maxSteps && !state.equals(AgentState.COMPLETED)) {
				currentStep++;
				log.info("Executing round {}/{}", currentStep, maxSteps);

				AgentExecResult stepResult = step();

				if (isStuck()) {
					handleStuckState(agentRecord);
				}
				else {
					// 更新全局状态以保持一致性
					log.info("Agent state: {}", stepResult.getState());
					state = stepResult.getState();
				}

				results.add("Round " + currentStep + ": " + stepResult.getResult());

				// Update agent record after each step
				agentRecord.setCurrentStep(currentStep);
			}

			if (currentStep >= maxSteps) {
				results.add("Terminated: Reached max rounds (" + maxSteps + ")");
			}

			// Set final state in record
			agentRecord.setEndTime(LocalDateTime.now());
			agentRecord.setStatus(state.toString());
			agentRecord.setCompleted(state.equals(AgentState.COMPLETED));

			// Calculate execution time in seconds
			long executionTimeSeconds = java.time.Duration.between(agentRecord.getStartTime(), agentRecord.getEndTime())
				.getSeconds();
			String status = agentRecord.isCompleted() ? "成功" : (agentRecord.isStuck() ? "执行卡住" : "未完成");
			agentRecord.setResult(String.format("执行%s [耗时%d秒] [消耗步骤%d] ", status, executionTimeSeconds, currentStep));

		}
		catch (Exception e) {
			log.error("Agent execution failed", e);
			// 记录异常信息到agentRecord
			agentRecord.setErrorMessage(e.getMessage());
			agentRecord.setCompleted(false);
			agentRecord.setEndTime(LocalDateTime.now());
			agentRecord.setResult(String.format("执行失败 [错误: %s]", e.getMessage()));
			results.add("Execution failed: " + e.getMessage());
			throw e; // 重新抛出异常，让上层调用者知道发生了错误
		}
		finally {
			state = AgentState.COMPLETED; // Reset state after execution

			agentRecord.setStatus(state.toString());
			llmService.clearAgentMemory(planId);
		}
		return results.isEmpty() ? "" : results.get(results.size() - 1);
	}

	protected abstract AgentExecResult step();

	private void handleStuckState(AgentExecutionRecord agentRecord) {
		log.warn("Agent stuck detected - Missing tool calls");

		// End current step
		setState(AgentState.COMPLETED);

		String stuckPrompt = """
				Agent response detected missing required tool calls.
				Please ensure each response includes at least one tool call to progress the task.
				Current step: %d
				Execution status: Force terminated
				""".formatted(currentStep);

		// Update agent record
		agentRecord.setStuck(true);
		agentRecord.setErrorMessage(stuckPrompt);
		agentRecord.setStatus(state.toString());

		log.error(stuckPrompt);
	}

	/**
	 * 检查是否处于卡住状态
	 */
	protected boolean isStuck() {
		// 目前判断是如果三次没有调用工具就认为是卡住了，就退出当前step。
		List<Message> memoryEntries = llmService.getAgentMemory().get(getPlanId());
		int zeroToolCallCount = 0;
		for (Message msg : memoryEntries) {
			if (msg instanceof AssistantMessage assistantMsg) {
                if (assistantMsg.getToolCalls().isEmpty()) {
					zeroToolCallCount++;
				}
			}
		}
		return zeroToolCallCount >= 3;
	}

    /**
	 * 获取智能体的数据上下文
	 *
	 * 使用说明： 1. 返回智能体在执行过程中需要的所有上下文数据 2. 数据可包含： - 当前执行状态 - 步骤信息 - 中间结果 - 配置参数 3.
	 * 数据在run()方法执行时通过setData()设置
	 *
	 * 不要修改这个方法的实现，如果你需要传递上下文，继承并修改setData方法，这样可以提高getData()的的效率。
	 * @return 包含智能体上下文数据的Map对象
	 */
	protected final Map<String, Object> getInitSettingData() {
		return initSettingData;
	}

    @Getter
    public static class AgentExecResult {

		private String result;

		private AgentState state;

		public AgentExecResult(String result, AgentState state) {
			this.result = result;
			this.state = state;
		}

    }

    public void setEnvData(Map<String, Object> envData) {
		this.envData = Map.copyOf(envData);
	}

}
