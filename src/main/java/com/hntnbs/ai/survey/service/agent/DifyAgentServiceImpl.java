package com.hntnbs.ai.survey.service.agent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hntnbs.ai.survey.dto.ExecuteAgentActionReq;
import com.hntnbs.ai.survey.dto.ExecuteAgentsReq;
import com.hntnbs.ai.survey.dto.SessionReq;
import com.hntnbs.ai.survey.model.TEAiConversation;
import com.hntnbs.ai.survey.model.TEAiMessage;
import com.hntnbs.ai.survey.service.TEAiConversationService;
import com.hntnbs.ai.survey.service.TEAiMessageService;
import com.hntnbs.ai.survey.service.impl.QdrantVectorService;
import com.hntnbs.ai.survey.utils.DifyClient;
import com.hntnbs.ai.survey.utils.handler.SseResponseHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
@ConditionalOnProperty(name = "tnbs.workflow_tools.type", havingValue = "dify")
public class DifyAgentServiceImpl implements IAgentService {

    @Autowired
    private TEAiMessageService messageService;

    @Autowired
    private TEAiConversationService conversationService;

    @Autowired
    private QdrantVectorService qdrantVectorService;


    @Override
    public SseEmitter execute(ExecuteAgentActionReq request) {
        // 初始化SseEmitter，设置60分钟超时
        SseEmitter emitter = new SseEmitter(600_000L);
        // 处理conversation
        DifyClient clientInstance;
        final String conversation = request.sessionId();
        clientInstance = DifyClient.getClientInstance(conversation);
        // 获取历史消息
        if (StrUtil.isNotEmpty(conversation)){
            List<TEAiMessage> history = messageService.lambdaQuery()
                    .eq(TEAiMessage::getConversationId, conversation)
                    .list();
            clientInstance.setHistoryMessages(JSONArray.copyOf(history));
        }
        // 异步执行streamConversation
        StringBuffer builder = new StringBuffer();
        CompletableFuture.runAsync(() -> {
            try {
                clientInstance.streamConversation(
                        request.operatorEntity().userId(),
                        request.question(),
                        request.env(),
                        new SseResponseHandler() {
                            @Override
                            public void handle(JSONObject data) {
                                try {
                                    String answer = Optional.ofNullable(data.getString("answer"))
                                            .orElse(data.getString("answer"));
                                    if (answer != null) {
                                        builder.append(answer);
                                        System.err.print(answer);
                                    }
                                    emitter.send(data);
                                } catch (IOException e) {
                                    emitter.completeWithError(e);
                                }
                            }

                            @Override
                            public void onError(Exception ex) {
                                emitter.completeWithError(ex);
                            }

                            @Override
                            public void onComplete() {
                                String conversationId = clientInstance.getConversationId();
                                if (StrUtil.isEmpty(request.sessionId())){
                                    TEAiConversation conversationSave = new TEAiConversation(
                                            conversationId,
                                            null,
                                            request.operatorEntity().userId(),
                                            new Date(),
                                            new Date(),
                                            0,
                                            "新建会话",
                                            0,
                                            0,
                                            request.env().getString("proId"),
                                            "dify"
                                    );
                                    conversationService.save(conversationSave);
                                }
                                TEAiMessage userMessage = new TEAiMessage();
                                userMessage.setContent(request.question());
                                userMessage.setConversationId(conversationId);
                                userMessage.setId(IdWorker.getIdStr());
                                userMessage.setRole("user");
                                userMessage.setReference("");
                                userMessage.setMessageId(IdWorker.getIdStr());
                                TEAiMessage modelMessage = new TEAiMessage();
                                modelMessage.setContent(builder.toString());
                                modelMessage.setConversationId(conversationId);
                                modelMessage.setId(IdWorker.getIdStr());
                                modelMessage.setRole("assistant");
                                modelMessage.setReference("");
                                modelMessage.setMessageId(IdWorker.getIdStr());
                                messageService.saveBatch(ListUtil.toList(modelMessage, userMessage));
                                JSONObject sseEnd = JSONObject.parse("{\"code\":0,\"data\":true}");
                                try {
                                    emitter.send(sseEnd);/*与前端约定协议最后一个消息，data使用true来标志*/
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                                emitter.complete(); // 完成SSE连接
                            }
                        });
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
        });

        // 立即返回SseEmitter
        return emitter;
    }

    @Override
    public String executeAgents(ExecuteAgentsReq request) {
        return "";
    }

    @Override
    public JSONObject selectListByUserId(SessionReq req) {
        String userId = req.operatorEntity().userId();
        String sessionId = req.sessionId();
        String proId = req.proId();
        List<TEAiConversation> list = conversationService.lambdaQuery()
                .eq(TEAiConversation::getUserId, userId)
                .eq(StrUtil.isNotEmpty(sessionId), TEAiConversation::getId, sessionId)
                .eq(TEAiConversation::getProId, proId)
                .eq(TEAiConversation::getType, "dify")
                .isNotNull(TEAiConversation::getProId)
                .list();
        if (CollectionUtil.isEmpty(list)) return null;
        TEAiConversation conversation = list.getFirst();
        return DifyClient.getSessions(req.operatorEntity().userId(), conversation.getId());
    }

    @Override
    public void deleteSession(String userId, String sessionId) {
        DifyClient client = DifyClient.getClientInstance(sessionId);
        client.deleteSession(userId);
    }

    @Override
    public ResponseEntity<Map<String, Object>> executeAgentActionAsync(Map<String, String> request) {
        return null;
    }
}