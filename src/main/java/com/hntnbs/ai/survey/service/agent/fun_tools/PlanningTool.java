package com.hntnbs.ai.survey.service.agent.fun_tools;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hntnbs.ai.survey.context.ExecutionPlan;
import com.hntnbs.ai.survey.context.ExecutionStep;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.api.OpenAiApi.FunctionTool;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Getter
@Slf4j
public class PlanningTool implements Function<Map<String, Object>, String> {

    private ExecutionPlan currentPlan;

    public String getCurrentPlanId() {
        return currentPlan != null ? currentPlan.getPlanId() : null;
    }

    private static final String PARAMETERS = """
            {
                "type": "object",
                "properties": {
                    "command": {
                        "description": "create a execution plan , Available commands: create",
                        "enum": [
                            "create"
                        ],
                        "type": "string"
                    },
                    "title": {
                        "description": "Title for the plan",
                        "type": "string"
                    },
                    "steps": {
                        "description": "List of plan steps",
                        "type": "array",
                        "items": {
                            "type": "string"
                        }
                    }
                },
                "required": [
                	"command",
                	"title",
                	"steps"
                ]
            }
            """;

    private static final String name = "planning";

    private static final String description = "Planning tool for managing tasks ";

    public FunctionTool getToolDefinition() {
        return new FunctionTool(new FunctionTool.Function(description, name, PARAMETERS));
    }

    // Parameterized FunctionToolCallback with appropriate types.

    public FunctionToolCallback<Map<String, Object>, String> getFunctionToolCallback() {
        return FunctionToolCallback.builder(name, this)
                .description(description)
                .inputSchema(PARAMETERS)
                .inputType(Map.class)
                .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
                .build();
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public String run(Map<String, Object> input) {
        try {
            String command = (String) input.get("command");
            String planId = (String) input.get("plan_id");
            String title = (String) input.get("title");
            List<String> steps = objectMapper.convertValue(input.get("steps"), new TypeReference<List<String>>() {
            });

            return switch (command) {
                case "create" -> createPlan(planId, title, steps);
                // case "update" -> updatePlan(planId, title, steps);
                // case "get" -> getPlan(planId);
                // case "mark_step" -> markStep(planId, stepIndex, stepStatus, stepNotes);
                // case "delete" -> deletePlan(planId);
                default -> {
                    log.info("收到无效的命令: {}", command);
                    throw new IllegalArgumentException("Invalid command: " + command);
                }
            };
        } catch (Exception e) {
            log.info("执行计划工具时发生错误", e);
            return "Error executing planning tool: " + e.getMessage();
        }
    }

    /**
     * 创建单个执行步骤
     *
     * @param step  步骤描述
     * @param index 步骤索引
     * @return 创建的ExecutionStep实例
     */
    private ExecutionStep createExecutionStep(String step, int index) {
        ExecutionStep executionStep = new ExecutionStep();
        executionStep.setStepIndex(index);
        executionStep.setStepRequirement(step);
        return executionStep;
    }

    public String createPlan(String planId, String title, List<String> steps) {
        if (title == null || steps == null || steps.isEmpty()) {
            log.info("创建计划时缺少必要参数: planId={}, title={}, steps={}", planId, title, steps);
            return "Required parameters missing";
        }

        ExecutionPlan plan = new ExecutionPlan(planId, title);
        // 使用新的createExecutionStep方法创建并添加步骤
        int index = 0;
        for (String step : steps) {
            plan.addStep(createExecutionStep(step, index++));
        }

        this.currentPlan = plan;
        return "Plan created: " + planId + "\n" + plan.getPlanExecutionStateStringFormat(false);
    }

    @Override
    public String apply(Map<String, Object> input) {
        return run(input);
    }

}
