package com.hntnbs.ai.survey.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AppConfig {

    public static String RAG_AGENT_BASE_URL;
    public static String RAG_AGENT_API_KEY;

    @Value("${tnbs.workflow_tools.base-url}")
    public void setBaseUrl(String url) {
        RAG_AGENT_BASE_URL = url;
    }

    @Value("${tnbs.workflow_tools.api-key}")
    public void setApiKey(String key) {
        RAG_AGENT_API_KEY = key;
    }
}
