package com.hntnbs.ai.survey.context.properties;
import com.hntnbs.ai.survey.config.ConfigService;
import com.hntnbs.ai.survey.config.options.ConfigInputType;
import com.hntnbs.ai.survey.config.options.ConfigOption;
import com.hntnbs.ai.survey.config.options.ConfigProperty;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "tnbs.ai")
public class TnbsAIProperties {

	@Lazy
	@Autowired
	private ConfigService configService;

	@Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "browser", key = "headless", path = "tnbs.ai.browser.headless",
			description = "是否使用无头浏览器模式", defaultValue = "false", inputType = ConfigInputType.CHECKBOX,
			options = { @ConfigOption(value = "true", label = "是"), @ConfigOption(value = "false", label = "否") })
	//暂时预留，按照tnbs.ai的代理行为设计,后期业务如果需要增加浏览器自动化的相关操作，可使用此字段
	private volatile Boolean browserHeadless;

	public Boolean getBrowserHeadless() {
		String configPath = "tnbs.ai.browser.headless";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			browserHeadless = Boolean.valueOf(value);
		}
		return browserHeadless;
	}

    @Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "interaction", key = "openBrowser", path = "tnbs.ai.openBrowserAuto",
			description = "启动时自动打开浏览器", defaultValue = "true", inputType = ConfigInputType.CHECKBOX,
			options = { @ConfigOption(value = "true", label = "是"), @ConfigOption(value = "false", label = "否") })
	private volatile Boolean openBrowserAuto;

	public Boolean getOpenBrowserAuto() {
		String configPath = "tnbs.ai.openBrowserAuto";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			openBrowserAuto = Boolean.valueOf(value);
		}
		return openBrowserAuto;
	}

	@Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "agent", key = "debug", path = "tnbs.ai.agent.debug",
			description = "提示词debug模式", defaultValue = "false", inputType = ConfigInputType.CHECKBOX,
			options = { @ConfigOption(value = "true", label = "是"), @ConfigOption(value = "false", label = "否") })
	private volatile Boolean agentDebug;

	@Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "agent", key = "maxSteps", path = "tnbs.ai.maxSteps",
			description = "智能体执行最大步数", defaultValue = "20", inputType = ConfigInputType.NUMBER)
	private volatile Integer maxSteps;

    public Boolean getAgentDebug() {
		String configPath = "tnbs.ai.browser.debug";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			agentDebug = Boolean.valueOf(value);
		}
		return agentDebug;
	}

    public Integer getMaxSteps() {
		String configPath = "tnbs.ai.maxSteps";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			maxSteps = Integer.valueOf(value);
		}
		return maxSteps;
	}

    @Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "agent", key = "resetAgents", path = "tnbs.ai.resetAgents",
			description = "重置所有agent", defaultValue = "true", inputType = ConfigInputType.CHECKBOX,
			options = { @ConfigOption(value = "true", label = "是"), @ConfigOption(value = "false", label = "否") })
	private volatile Boolean resetAgents;

	public Boolean getResetAgents() {
		String configPath = "tnbs.ai.resetAgents";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			resetAgents = Boolean.valueOf(value);
		}
		return resetAgents;
	}

    @Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "general", key = "baseDir", path = "tnbs.ai.baseDir",
			description = "tnbs.ai根目录", defaultValue = "", inputType = ConfigInputType.TEXT)
	private volatile String baseDir = "";

	public String getBaseDir() {
		String configPath = "tnbs.ai.baseDir";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			baseDir = value;
		}
		return baseDir;
	}

    @Setter
    @ConfigProperty(group = "tnbs.ai", subGroup = "agent", key = "userInputTimeout",
			path = "tnbs.ai.agent.userInputTimeout", description = "用户输入表单等待超时时间(秒)", defaultValue = "300",
			inputType = ConfigInputType.NUMBER)
	private volatile Integer userInputTimeout;

	public Integer getUserInputTimeout() {
		String configPath = "tnbs.ai.agent.userInputTimeout";
		String value = configService.getConfigValue(configPath);
		if (value != null) {
			userInputTimeout = Integer.valueOf(value);
		}
		// Ensure a default value if not configured and not set
		if (userInputTimeout == null) {
			// Attempt to parse the default value specified in the annotation,
			// or use a hardcoded default if parsing fails or is complex to retrieve here.
			// For simplicity, directly using the intended default.
			userInputTimeout = 3000;
		}
		return userInputTimeout;
	}

}
